<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SIGMA - Dashboard</title>
  
  <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-functions-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-storage-compat.js"></script>
  
  <style>
    <?!= include('css/common.css'); ?>
    <?!= include('css/dashboard.css'); ?>
  </style>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
  <header class="dashboard-header">
    <div class="header-content">
      <div class="header-left">
        <h1>📊 Dashboard SIGMA</h1>
        <div class="refresh-indicator" id="refresh-indicator">🔄 Temps-réel actif</div>
      </div>
      <div class="header-right">
        <div class="performance-metrics" id="performance-metrics"></div>
        <div class="user-info" id="user-info">
          <span id="user-name">Utilisateur</span>
          <button class="btn btn-outline" id="logout-btn-dashboard">Déconnexion</button>
        </div>
      </div>
    </div>
  </header>
  <main class="dashboard-container">
    <div class="dashboard-grid">
      <div class="dashboard-card critical" id="stock-alerts"></div>
      <div class="dashboard-card" id="missing-material"></div>
      <div class="dashboard-card critical" id="overdue-emprunts"></div>
      <div class="dashboard-card info" id="upcoming-emprunts"></div>
      <div class="dashboard-card warning" id="non-op-modules"></div>
      <div class="dashboard-card warning" id="non-op-material"></div>
      <div class="dashboard-card" id="pending-emprunts"></div>
    </div>
  </main>
  <script>
    <?!= include('js/firebase-config.js'); ?>
    <?!= include('js/auth.js'); ?>
    <?!= include('js/dashboard/PaginationManager.js'); ?>
    <?!= include('js/dashboard/DashboardUI.js'); ?>
    <?!= include('js/dashboard/DashboardManager.js'); ?>
    <?!= include('js/dashboard/dashboard-init.js'); ?>

    document.getElementById('logout-btn-dashboard').addEventListener('click', () => {
        if (window.authManager) {
            window.authManager.signOut();
        }
    });
  </script>
</body>
</html>
